name: Create production build and submit

on:
  push:
    tags:
      - 'v*'

jobs:
  # build_ios:
  #   name: Build iOS Production
  #   type: build
  #   params:
  #     platform: ios
  #     profile: preview

  # build_android:
  #   name: Build Android Production
  #   type: build
  #   params:
  #     platform: android
  #     profile: preview

  submit_ios:
    name: Submit iOS to App Store
    # needs: [build_ios]
    type: submit
    params:
      build_id: f971ee47-011e-4c51-b108-f6ef4dee9055
      profile: preview

  # submit_android:
  #   name: Submit Android to Play Store
  #   needs: [build_android]
  #   type: submit
  #   params:
  #     build_id: ${{ needs.build_android.outputs.build_id }}
  #     profile: preview
